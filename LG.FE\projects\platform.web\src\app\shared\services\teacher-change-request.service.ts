import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// Shared Library
import {
  AuthStateService,
  HandleApiResponseService,
  ICreateTeacherChangeRequestRequest,
  ICreateTeacherChangeRequestResponse,
  IGetTeachersToChangeRequest,
  IGetTeachersToChangeResponse,
  ITeacherToChangeWithAffectedResourcesDto,
  ITeacherChangeRequestSourcesEnum,
  IBasicProfileInfoDto,
  IAffectedStudentPackageDto,
  ITeacherChangeRequest
} from 'SharedModules.Library';

// Local Interfaces
import { TeacherChangeType } from '../../features/change-teacher-request/interfaces/change-teacher-request.interfaces';


/**
 * Interface for available teachers data
 */
export interface IAvailableTeachersData {
  /** List of available teachers */
  teachers: IBasicProfileInfoDto[];
  /** Affected student packages */
  affectedPackages: IAffectedStudentPackageDto[];
}

/**
 * Service for handling teacher change requests
 * Provides centralized API calls and business logic for teacher change functionality
 */
@Injectable({
  providedIn: 'root'
})
export class TeacherChangeRequestService {
  // Injected services
  private readonly authStateService = inject(AuthStateService);
  private readonly apiService = inject(HandleApiResponseService);

  /**
   * Create a base teacher change request object
   * @param parentId Optional parent ID (defaults to current user)
   * @returns Partial ICreateTeacherChangeRequestRequest with common fields
   */
  createBaseRequest(parentId?: string): Partial<ICreateTeacherChangeRequestRequest> {
    const requestParentId = parentId || this.authStateService.getUserClaims()?.id;

    if (!requestParentId) {
      throw new Error('Parent ID is required to create teacher change request');
    }

    return {
      parentId: requestParentId,
      source: ITeacherChangeRequestSourcesEnum.DirectRequest
    };
  }

  /**
   * Validate a teacher change request object
   * @param request The request object to validate
   * @returns Validation result with errors if any
   */
  validateRequest(request: Partial<ICreateTeacherChangeRequestRequest>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.parentId?.trim()) {
      errors.push('Parent ID is required');
    }

    if (!request.teacherId?.trim()) {
      errors.push('Teacher selection is required');
    }

    if (!request.reason?.trim()) {
      errors.push('Reason for change is required');
    } else if (request.reason.length < 10) {
      errors.push('Reason must be at least 10 characters long');
    } else if (request.reason.length > 1000) {
      errors.push('Reason cannot exceed 1000 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get available teachers for change request with optional filtering parameters
   * @param params Parameters for loading teachers
   * @returns Observable with available teachers and affected packages
   */
  getAvailableTeachers(params?: IGetTeachersToChangeRequest): Observable<IAvailableTeachersData> {
    console.log('🔍 TeacherChangeRequestService.getAvailableTeachers called - using shared API call logic');

    return this.getRawTeacherData(params || {}).pipe(
      map((teachersData: ITeacherToChangeWithAffectedResourcesDto[]) => {
        console.log('🔍 Processing raw teacher data for getAvailableTeachers format');

        // Extract teachers from the response
        const teachers: IBasicProfileInfoDto[] = [];
        const affectedPackages: IAffectedStudentPackageDto[] = [];

        // Process teachers from affected resources
        teachersData.forEach(teacherData => {
          // Get teacher info from the first affected student package
          const firstPackage = teacherData.affectedStudentPackages[0];
          // The API response includes additional properties not in the interface definition
          const packageWithTeacher = firstPackage as IAffectedStudentPackageDto & { teacher: IBasicProfileInfoDto };

          if (packageWithTeacher?.teacher && teacherData.teacherToChangeId) {
            // Check if we already have this teacher (avoid duplicates)
            const existingTeacher = teachers.find(t => t.userId === teacherData.teacherToChangeId);
            if (!existingTeacher) {
              teachers.push(packageWithTeacher.teacher);
            }
          }
          if (teacherData.affectedStudentPackages) {
            affectedPackages.push(...teacherData.affectedStudentPackages);
          }
        });

        return {
          teachers,
          affectedPackages
        };
      })
    );
  }

  /**
   * Load available teachers with specific parameters for student/group changes
   * This returns raw teacher data format for components that need the full structure
   * @param params Parameters for loading teachers
   * @returns Observable with teachers to change data
   */
  loadAvailableTeachers(params: IGetTeachersToChangeRequest): Observable<ITeacherToChangeWithAffectedResourcesDto[]> {
    console.log('🔍 TeacherChangeRequestService.loadAvailableTeachers called - using direct API call');

    return this.getRawTeacherData(params);
  }

  /**
   * Internal method to get raw teacher data directly from API
   * This is the single source of truth for all teacher loading API calls
   * @param params Parameters for loading teachers
   * @returns Observable with raw teachers to change data
   */
  private getRawTeacherData(params: {
    teachingLanguageId?: string;
    studentId?: string;
    groupId?: string;
    parentId?: string;
  }): Observable<ITeacherToChangeWithAffectedResourcesDto[]> {
    const requestParentId = params.parentId || this.authStateService.getUserClaims()?.id;

    if (!requestParentId) {
      throw new Error('Parent ID is required to load available teachers');
    }

    // Build request parameters based on IGetTeachersToChangeRequest
    const request: IGetTeachersToChangeRequest = {
      parentId: requestParentId,
      teachingLanguageId: params.teachingLanguageId,
      studentId: params.studentId,
      groupId: params.groupId
    };

    console.log('🔍 TeacherChangeRequestService.getRawTeacherData called with params:', params);
    console.log('🔍 Built request object:', request);
    console.log('🔍 Using API endpoint:', ITeacherChangeRequest.getTeachersToChangeRequest);

    return this.apiService.getApiData<IGetTeachersToChangeResponse>({
      url: ITeacherChangeRequest.getTeachersToChangeRequest,
      method: 'GET'
    }, request).pipe(
      map((response: IGetTeachersToChangeResponse) => {
        console.log('🔍 API Response received:', response);
        console.log('🔍 Teachers to change count:', response.teachersToChangeWithAffectedResources?.length || 0);

        const teachersData = response.teachersToChangeWithAffectedResources || [];

        // Log each teacher's details for debugging
        teachersData.forEach((teacher, index) => {
          console.log(`🔍 Teacher ${index + 1}:`, {
            teacherToChangeId: teacher.teacherToChangeId,
            affectedPackagesCount: teacher.affectedStudentPackages?.length || 0
          });
        });

        return teachersData;
      })
    );
  }

  /**
   * Create a complete teacher change request
   * @param request Complete teacher change request data
   * @returns Observable with request response
   */
  createCompleteTeacherChangeRequest(request: ICreateTeacherChangeRequestRequest): Observable<ICreateTeacherChangeRequestResponse> {
    const apiRequest: ICreateTeacherChangeRequestRequest = {
      parentId: request.parentId,
      teacherId: request.teacherId,
      reason: request.reason,
      source: request.source
      // studentId, groupId, teachingLanguageId, adminNotes are not needed for complete changes
    };

    return this.apiService.getApiData<ICreateTeacherChangeRequestResponse>(
      { url: ITeacherChangeRequest.postTeacherChangeRequest, method: 'POST' },
      apiRequest
    ).pipe(
      map((response: ICreateTeacherChangeRequestResponse) => ({
        success: true,
        requestId: response.requestId,
        message: 'Teacher change request created successfully'
      }))
    );
  }

  /**
   * Create a student/group specific teacher change request
   * @param request Student/group teacher change request data
   * @returns Observable with request response
   */
  createStudentGroupTeacherChangeRequest(request: ICreateTeacherChangeRequestRequest): Observable<ICreateTeacherChangeRequestResponse> {
    const apiRequest: ICreateTeacherChangeRequestRequest = {
      parentId: request.parentId,
      teacherId: request.teacherId,
      studentId: request.studentId,
      groupId: request.groupId,
      teachingLanguageId: request.teachingLanguageId,
      reason: request.reason,
      source: request.source,
      adminNotes: request.adminNotes
    };

    return this.apiService.getApiData<ICreateTeacherChangeRequestResponse>(
      { url: ITeacherChangeRequest.postTeacherChangeRequest, method: 'POST' },
      apiRequest
    ).pipe(
      map((response: ICreateTeacherChangeRequestResponse) => ({
        success: true,
        requestId: response.requestId,
        message: 'Teacher change request created successfully'
      }))
    );
  }

  /**
   * Create teacher change request based on type
   * @param changeType Type of teacher change
   * @param teacherId Selected teacher ID
   * @param reason Reason for change
   * @param parentId Optional parent ID (defaults to current user)
   * @param additionalData Additional data for student/group changes
   * @returns Observable with request response
   */
  createTeacherChangeRequest(
    changeType: TeacherChangeType,
    teacherId: string,
    reason: string,
    parentId?: string,
    additionalData?: {
      studentId?: string;
      groupId?: string;
      teachingLanguageId?: string;
      adminNotes?: string;
    }
  ): Observable<ICreateTeacherChangeRequestResponse> {
    const requestParentId = parentId || this.authStateService.getUserClaims()?.id;
    
    if (!requestParentId) {
      throw new Error('Parent ID is required to create teacher change request');
    }

    switch (changeType) {
      case TeacherChangeType.COMPLETE_CHANGE:
        return this.createCompleteTeacherChangeRequest({
          parentId: requestParentId,
          teacherId,
          reason,
          source: ITeacherChangeRequestSourcesEnum.Parent
        });

      case TeacherChangeType.STUDENT_GROUP_CHANGE:
        return this.createStudentGroupTeacherChangeRequest({
          parentId: requestParentId,
          teacherId,
          reason,
          source: ITeacherChangeRequestSourcesEnum.Parent,
          studentId: additionalData?.studentId,
          groupId: additionalData?.groupId,
          teachingLanguageId: additionalData?.teachingLanguageId,
          adminNotes: additionalData?.adminNotes
        });

      default:
        throw new Error(`Unsupported teacher change type: ${changeType}`);
    }
  }

  /**
   * Get current parent ID from auth service
   * @returns Current parent ID or null
   */
  getCurrentParentId(): string | null {
    return this.authStateService.getUserClaims()?.id || null;
  }

  /**
   * Validate teacher change request data
   * @param changeType Type of teacher change
   * @param teacherId Selected teacher ID
   * @param reason Reason for change
   * @param additionalData Additional data for validation
   * @returns Validation result with errors if any
   */
  validateTeacherChangeRequest(
    changeType: TeacherChangeType,
    teacherId: string,
    reason: string,
    additionalData?: {
      studentId?: string;
      groupId?: string;
      teachingLanguageId?: string;
    }
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Common validations
    if (!teacherId?.trim()) {
      errors.push('Teacher selection is required');
    }

    if (!reason?.trim()) {
      errors.push('Reason for change is required');
    }

    if (reason?.trim().length < 10) {
      errors.push('Reason must be at least 10 characters long');
    }

    // Type-specific validations
    if (changeType === TeacherChangeType.STUDENT_GROUP_CHANGE) {
      if (!additionalData?.studentId && !additionalData?.groupId) {
        errors.push('Either student or group must be selected for student/group changes');
      }

      if (additionalData?.studentId && additionalData?.groupId) {
        errors.push('Cannot select both student and group for the same request');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
