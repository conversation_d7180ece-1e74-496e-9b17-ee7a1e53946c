// ============================================================================
// PACKAGES LIST COMPONENT STYLES
// ============================================================================

.packages-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .applied-filters-container {
    padding: 0 1rem;
  }

  .content-wrapper {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;

    .table-container {
      flex: 1;
      padding: 0 1rem 1rem;
      overflow: auto;
    }
  }
}

// ============================================================================
// TABLE CELL STYLES
// ============================================================================

.package-name-cell {
  .font-semibold {
    color: var(--text-color);
    font-weight: 600;
  }
}

.students-cell {
  .student-count {
    font-weight: 600;
    color: var(--primary-color);
  }

  .student-names {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.teacher-cell {
  .teacher-name {
    font-weight: 500;
    color: var(--text-color);
  }
}

.lessons-progress-cell {
  .progress-text {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .progress-bar-container {
    width: 100%;
    height: 6px;
    background-color: var(--surface-200);
    border-radius: 3px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), var(--primary-color-text));
      transition: width 0.3s ease;
    }
  }
}

.expiration-cell {
  display: flex;
  align-items: center;

  &.expiring-soon {
    color: var(--orange-500);
    font-weight: 500;
  }

  &.expired {
    color: var(--red-500);
    font-weight: 500;
  }
}

.payment-status-cell {
  .payment-amounts {
    line-height: 1.3;

    div {
      margin-bottom: 0.125rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.actions-cell {
  .p-button {
    margin: 0 0.125rem;
  }
}

// ============================================================================
// PACKAGE TYPE BADGES
// ============================================================================

.package-type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.package-type-0 { // FreeTrial
    background-color: var(--blue-100);
    color: var(--blue-800);
  }

  &.package-type-1 { // Paid
    background-color: var(--green-100);
    color: var(--green-800);
  }

  &.package-type-2 { // Gift
    background-color: var(--purple-100);
    color: var(--purple-800);
  }
}

// ============================================================================
// PACKAGE STATUS BADGES
// ============================================================================

.package-status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.package-status-0 { // Active
    background-color: var(--green-100);
    color: var(--green-800);
  }

  &.package-status-1 { // InActive
    background-color: var(--yellow-100);
    color: var(--yellow-800);
  }

  &.package-status-2 { // Expired
    background-color: var(--red-100);
    color: var(--red-800);
  }

  &.package-status-3 { // Completed
    background-color: var(--blue-100);
    color: var(--blue-800);
  }

  &.package-status-4 { // Refunded
    background-color: var(--orange-100);
    color: var(--orange-800);
  }
}

// ============================================================================
// EMPTY STATE STYLES
// ============================================================================

.empty-state {
  padding: 2rem;
  text-align: center;

  .pi-inbox {
    display: block;
    margin: 0 auto 1rem;
    color: var(--text-color-secondary);
  }

  h3 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--text-color-secondary);
    max-width: 400px;
    margin: 0 auto;
  }
}

.loading-message {
  padding: 2rem;
  color: var(--text-color-secondary);
  font-size: 1rem;

  .pi-spinner {
    color: var(--primary-color);
  }
}

// ============================================================================
// RESPONSIVE DESIGN
// ============================================================================

@media (max-width: 768px) {
  .packages-list-container {
    .content-wrapper {
      .table-container {
        padding: 0 0.5rem 0.5rem;
      }
    }
  }

  .students-cell .student-names {
    max-width: 150px;
  }

  .payment-status-cell {
    font-size: 0.75rem;
  }

  .actions-cell .p-button {
    margin: 0 0.0625rem;
  }
}

@media (max-width: 576px) {
  .package-type-badge,
  .package-status-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }

  .lessons-progress-cell .progress-text {
    font-size: 0.75rem;
  }

  .students-cell .student-names {
    max-width: 120px;
  }
}

// ============================================================================
// EXPANSION STYLES
// ============================================================================

.expansion-toggle-btn {
  background: none !important;
  border: none !important;
  padding: 0.5rem !important;
  color: var(--text-color-secondary) !important;
  transition: color 0.2s ease;

  &:hover {
    color: var(--primary-color) !important;
    background: var(--surface-100) !important;
  }

  .pi {
    font-size: 0.875rem;
    transition: transform 0.2s ease;
  }
}

.expansion-content {
  border-left: 3px solid var(--primary-color);

  .expansion-section {
    h4 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      font-size: 1rem;
      font-weight: 600;

      .pi {
        color: var(--primary-color);
        font-size: 0.875rem;
      }
    }
  }

  .expansion-details {
    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 0.5rem;
      padding: 0.25rem 0;
      border-bottom: 1px solid var(--surface-200);

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .detail-label {
        font-weight: 500;
        color: var(--text-color-secondary);
        min-width: 60px;
        font-size: 0.875rem;
      }

      .detail-value {
        color: var(--text-color);
        text-align: right;
        font-size: 0.875rem;
        word-break: break-word;
        flex: 1;
        margin-left: 1rem;
      }
    }

    .student-item {
      border: 1px solid var(--surface-300);
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-row {
        margin-bottom: 0.25rem;
        padding: 0.125rem 0;
        border-bottom: 1px solid var(--surface-200);

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }
      }
    }
  }
}

// ============================================================================
// DARK MODE SUPPORT
// ============================================================================

:host-context(.dark-mode) {
  .progress-bar-container {
    background-color: var(--surface-700);
  }

  .expansion-content {
    background-color: var(--surface-800) !important;
    border-color: var(--primary-color);

    .expansion-details {
      .detail-row {
        border-bottom-color: var(--surface-600);
      }

      .student-item {
        background-color: var(--surface-700) !important;
        border-color: var(--surface-600);

        .detail-row {
          border-bottom-color: var(--surface-600);
        }
      }
    }
  }

  .expansion-toggle-btn {
    &:hover {
      background: var(--surface-700) !important;
    }
  }

  .package-type-badge,
  .package-status-badge {
    &.package-type-0,
    &.package-status-3 {
      background-color: var(--blue-900);
      color: var(--blue-200);
    }

    &.package-type-1,
    &.package-status-0 {
      background-color: var(--green-900);
      color: var(--green-200);
    }

    &.package-type-2 {
      background-color: var(--purple-900);
      color: var(--purple-200);
    }

    &.package-status-1 {
      background-color: var(--yellow-900);
      color: var(--yellow-200);
    }

    &.package-status-2 {
      background-color: var(--red-900);
      color: var(--red-200);
    }

    &.package-status-4 {
      background-color: var(--orange-900);
      color: var(--orange-200);
    }
  }
}
