// ============================================================================
// PACKAGES LIST FILTERS COMPONENT
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  computed,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  inject,
  signal,
  ViewChild
} from '@angular/core';
import { FormsModule } from '@angular/forms';

// === PRIMENG IMPORTS ===
import { DropdownModule } from 'primeng/dropdown';
import { SelectModule } from 'primeng/select';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetPackagesRequest,
  IPackageTypeEnum,
  IPackageStatusEnum,
  IEnumDropdownOptions,
  nameOf,
  PrimeStudentsSelectionComponent,
  PrimeTeachersSelectionComponent,
  BaseDataGridFiltersComponent,
  IFilterChangeEvent,
  IFilterActionEvent,
  IBaseFilterState,
  DateRangeFilterService,
  ISearchTeacherDto,
  ISearchStudentDto
} from 'SharedModules.Library';

export type IPackagesFilterChangeEvent = IFilterChangeEvent<IGetPackagesRequest>;

export type IPackagesFilterActionEvent = IFilterActionEvent<IGetPackagesRequest>;

/**
 * Interface for filter state data passed from parent
 */
export interface IPackagesFilterState extends IBaseFilterState<IGetPackagesRequest> {
  queryParams: IGetPackagesRequest;
  packageTypes: IEnumDropdownOptions[];
  packageStatuses: IEnumDropdownOptions[];
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface IPackagesFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Packages List Filters Component
 *
 * Provides filtering capabilities for the packages list including:
 * - Package type and status filters
 * - Date range filters (purchased, expires)
 * - Teacher and student selection
 * - Search functionality
 * - Teaching language filter
 */
@Component({
  selector: 'app-packages-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DropdownModule,
    SelectModule,
    MultiSelectModule,
    CheckboxModule,
    DatePickerModule,
    PrimeStudentsSelectionComponent,
    PrimeTeachersSelectionComponent
  ],
  templateUrl: './packages-list-filters.component.html',
  styleUrls: ['./packages-list-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackagesListFiltersComponent extends BaseDataGridFiltersComponent<
  IGetPackagesRequest,
  IPackagesFilterState,
  IPackagesFilterConfig
> implements OnInit, OnChanges {
  private dateRangeFilterService = inject(DateRangeFilterService);

  @Input() override filterState: IPackagesFilterState = {
    queryParams: {} as IGetPackagesRequest,
    packageTypes: [],
    packageStatuses: [],
    isFilterOpen: true
  };

  @Input() override config: IPackagesFilterConfig = {
    showToggleButton: true,
    defaultOpen: true,
    enableAutoSearch: false,
    searchDebounceMs: 400
  };

  @Output() override filterChanged = new EventEmitter<IPackagesFilterChangeEvent>();
  @Output() override filterAction = new EventEmitter<IPackagesFilterActionEvent>();

  // Computed properties that merge temp + current state
  override readonly currentFilters = computed(() => ({ ...this.filterState.queryParams, ...this._tempFilters() }));

  // Field names for type safety using nameOf with paramMap pattern
  private readonly paramsMap = nameOf<IGetPackagesRequest>();

  // ViewChild references to the selection components
  @ViewChild('teachersSelection') teachersSelectionComponent?: PrimeTeachersSelectionComponent;
  @ViewChild('studentsSelection') studentsSelectionComponent?: PrimeStudentsSelectionComponent;

  // Reset signal for prime selection components
  resetSelectionSignal = signal(false);

  // Computed properties for package types and statuses
  readonly packageTypes = computed(() => this.filterState.packageTypes || []);
  readonly packageStatuses = computed(() => this.filterState.packageStatuses || []);

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngOnChanges(): void {
    super.ngOnChanges();
  }

  // ============================================================================
  // FILTER CHANGE HANDLERS
  // ============================================================================

  /**
   * Handle filter changes using paramMap pattern
   */
  onFilterChange(filterName: keyof IGetPackagesRequest, value: any): void {
    this._tempFilters.update(current => ({ ...current, [filterName]: value }));
  }

  /**
   * Handle teacher selection
   */
  onTeacherSelected(teacher: ISearchTeacherDto | ISearchTeacherDto[]): void {
    console.log('Teacher selected in filters:', teacher);
    const teacherId = Array.isArray(teacher) ? teacher[0]?.id : teacher?.id || null;
    this.onFilterChange('teacherId', teacherId);
  }

  /**
   * Handle student selection
   */
  onStudentSelected(student: ISearchStudentDto | ISearchStudentDto[]): void {
    const studentId = Array.isArray(student) ? student[0]?.userId : student?.userId || null;
    this.onFilterChange('studentId', studentId);
  }

  // ============================================================================
  // DATE FILTER HANDLERS
  // ============================================================================

  /**
   * Handle purchased from date changes
   * Converts date to UTC using DateRangeFilterService for proper timezone handling
   */
  onPurchasedFromChange(value: Date | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateFromToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.paramsMap.purchasedFrom!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  /**
   * Handle purchased to date changes
   * Converts date to UTC using DateRangeFilterService for proper timezone handling
   */
  onPurchasedToChange(value: Date | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.paramsMap.purchasedTo!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  /**
   * Handle expires from date changes
   * Converts date to UTC using DateRangeFilterService for proper timezone handling
   */
  onExpiresFromChange(value: Date | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateFromToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.paramsMap.expiresFrom!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  /**
   * Handle expires to date changes
   * Converts date to UTC using DateRangeFilterService for proper timezone handling
   */
  onExpiresToChange(value: Date | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(value);
    this._tempFilters.update(current => ({
      ...current,
      [this.paramsMap.expiresTo!]: convertedDate ? new Date(convertedDate) : null
    }));
  }

  // ============================================================================
  // REQUIRED ABSTRACT METHOD IMPLEMENTATIONS
  // ============================================================================

  /**
   * Public method to reset filters to default state (for drawer reset button)
   * This only resets the temporary filter state, not the applied filters
   */
  resetFiltersToDefault(): void {
    this.resetAllFilters();
  }

  /**
   * Reset all filters to default values
   * Implementation of abstract method from BaseDataGridFiltersComponent
   */
  protected override resetAllFilters(): void {
    // Reset all temp filters to specific default values (not empty object)
    this._tempFilters.set({
      [this.paramsMap.searchTerm!]: null,
      [this.paramsMap.packageType!]: null,
      [this.paramsMap.packageStatus!]: null,
      [this.paramsMap.teacherId!]: null,
      [this.paramsMap.studentId!]: null,
      [this.paramsMap.groupId!]: null,
      [this.paramsMap.teachingLanguageId!]: null,
      [this.paramsMap.purchasedFrom!]: null,
      [this.paramsMap.purchasedTo!]: null,
      [this.paramsMap.expiresFrom!]: null,
      [this.paramsMap.expiresTo!]: null,
      [this.paramsMap.hasAddOnExtension!]: null,
      [this.paramsMap.parentAccountDeleted!]: null,
      [this.paramsMap.hasOutstandingTeacherPayments!]: false,
      [this.paramsMap.isTeacherAssigmentPage!]: false
    } as IGetPackagesRequest);

    // Trigger reset signal for prime selection components
    this.resetSelectionSignal.set(true);

    // Reset the signal back to false after a short delay to allow components to react
    setTimeout(() => {
      this.resetSelectionSignal.set(false);
    }, 200);
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  getBooleanOptions() {
    return [
      { label: 'Yes', value: true },
      { label: 'No', value: false }
    ];
  }
}
