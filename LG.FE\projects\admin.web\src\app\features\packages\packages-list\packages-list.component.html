<!-- ============================================================================ -->
<!-- PACKAGES LIST COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="packages-page-container">
  <!-- Main Content (Table & Applied Filters) -->
  <div class="main-content">
    <!-- Applied Filters Section -->
    <app-applied-filters-tags [filters]="appliedFilters()" (filterRemoved)="onAppliedFilterRemove($event)"
      (clearAllClicked)="onAppliedFiltersClearAll($event)">
      <p-button extraButton icon="pi pi-filter" label="Filters" severity="secondary" (click)="openFiltersDrawer()"
        class="" styleClass="p-button-sm"
        [badge]="appliedFiltersCount() > 0 ? appliedFiltersCount().toString() : undefined" badgeClass="p-badge-info" />
    </app-applied-filters-tags>

    <!-- Filters Drawer Sidebar -->
    <app-filters-drawer-sidebar #filtersDrawer [(visible)]="isFiltersDrawerVisible" [config]="filtersDrawerConfig"
      [filterContentTemplate]="filterContentTemplate" (actionClicked)="onFiltersDrawerAction($event)">
    </app-filters-drawer-sidebar>

    <!-- Filter Content Template -->
    <ng-template #filterContentTemplate>
      <app-packages-list-filters #packagesFilters [filterState]="packagesFilterState()"
        (filterChanged)="onFiltersChange($event)" (filterAction)="onFiltersAction($event)">
      </app-packages-list-filters>
    </ng-template>

    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
      <p-table #dt [value]="dataResponse()?.pageData || []" dataKey="id" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [first]="(queryParams().pageNumber - 1) * queryParams().pageSize"
        [totalRecords]="totalRecords()" [loading]="isLoading()" [rowsPerPageOptions]="rowsPerPageOptions()"
        [showCurrentPageReport]="true" [sortField]="queryParams().sortColumn"
        [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1" [reorderableColumns]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} packages" (onPage)="onPageChange($event)"
        (onSort)="onSortChange($event)" (onColReorder)="onColumnReorder($event)" [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows
        [tableStyle]="{'min-width': '1000px; min-height: calc(100% + 300px)'}" [columns]="selectedColumns()"
        [expandedRowKeys]="expandedRows()">

        <ng-template pTemplate="caption">
          <div class="flex flex-column md:flex-row align-items-center md:justify-content-between gap-3 p-0">
            <!-- Search Row - Full width on all screens -->
            <div class="flex align-items-center">
              <div class="field flex-1 m-0">
                <div class="search-input-container">
                  <input type="text" pInputText id="searchTerm" type="text" styleClass="w-full"
                    [value]="queryParams().searchTerm || ''" (input)="updateSearchTerm($event)"
                    placeholder="Search packages" />
                  <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                    (click)="clearSearchTerm()">
                    <i class="pi pi-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Actions Row - Responsive layout -->
            <div
              class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center justify-content-between gap-3">
              <!-- Column Selection and Export - Stack on mobile, inline on desktop -->
              <div class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center gap-2 sm:gap-3">
                <p-multiSelect [options]="visibleColumns()" [ngModel]="selectedColumns()"
                  (ngModelChange)="onColumnsChange($event)" [filter]="true" filterBy="header"
                  placeholder="Select Columns" optionLabel="header" class="column-selector flex-1 sm:flex-none"
                  styleClass="p-multiselect-sm" [showToggleAll]="false">
                </p-multiSelect>
                <p-button label="Export" icon="pi pi-download" severity="secondary" (onClick)="exportToExcel()"
                  class="p-button-sm flex-1 sm:flex-none" />
              </div>
            </div>
          </div>
        </ng-template>

        <!-- Table Header -->
        <ng-template pTemplate="header">
          <app-data-grid-header-footer [columns]="selectedColumns()" [sortable]="true" [reorderable]="true"
            [showHeader]="true" [showFooter]="false" [showActionsColumn]="false" [showExpansionColumn]="true">
          </app-data-grid-header-footer>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-package let-rowIndex="rowIndex">
          <tr>
            <!-- Expansion Column -->
            <td class="text-center" style="width: 50px;">
              <button type="button" class="p-button-text p-button-sm expansion-toggle-btn"
                (click)="toggleExpansion(package.id)" [attr.aria-expanded]="isExpanded(package.id)"
                [attr.aria-label]="isExpanded(package.id) ? 'Collapse details' : 'Expand details'">
                <i class="pi" [ngClass]="isExpanded(package.id) ? 'pi-chevron-down' : 'pi-chevron-right'"></i>
              </button>
            </td>

            @for (col of selectedColumns(); track col.field) {
            <td [style.max-width]="col.maxWidth">
              @switch (col.field) {

              <!-- Package ID -->
              @case ('id') {
              <span class="font-mono text-sm">{{ package.id | slice:0:8 }}...</span>
              }

              <!-- Package Name/Group -->
              @case ('groupName') {
              <div class="package-name-cell">
                @if (package.groupName) {
                <span class="font-semibold">{{ package.groupName }}</span>
                } @else {
                <span class="text-muted">Individual Package</span>
                }
                @if (package.teachingLanguageName) {
                <div class="text-sm text-muted">{{ package.teachingLanguageName }}</div>
                }
              </div>
              }

              <!-- Package Type -->
              @case ('packageType') {
              <span class="package-type-badge" [ngClass]="'package-type-' + package.packageType">
                {{ getPackageTypeText(package.packageType) }}
              </span>
              }

              <!-- Package Status -->
              @case ('packageStatus') {
              <span class="package-status-badge" [ngClass]="'package-status-' + package.packageStatus">
                {{ getPackageStatusText(package.packageStatus) }}
              </span>
              }

              <!-- Students -->
              @case ('numberOfStudents') {
              <div class="students-cell">
                <span class="student-count">{{ package.numberOfStudents }}</span>
              
              </div>
              }

              <!-- Teacher -->
              @case ('teacher') {
              @if (package.teacher) {
              <div class="teacher-cell">
                <span class="teacher-name">
                  {{ package.teacher.firstName }} {{ package.teacher.lastName }}
                </span>
              </div>
              } @else {
              <span class="text-muted">No Teacher</span>
              }
              }

              <!-- Total Lessons -->
              @case ('totalLessons') {
              <div class="lessons-cell text-center">
                <span class="font-semibold">{{ package.totalLessons || 0 }}</span>
              </div>
              }

              <!-- Remaining Lessons -->
              @case ('remainingLessons') {
              <div class="lessons-cell text-center">
                <span class="font-semibold">{{ package.remainingLessons || 0 }}</span>
              </div>
              }

              <!-- Expiration Date -->
              @case ('expiresOnDateUtc') {
              @if (package.expiresOnDateUtc) {
              <div class="expiration-cell">
                <span [ngClass]="getExpirationClass(package.expiresOnDateUtc)">
                  {{ formatDate(package.expiresOnDateUtc) }}
                </span>
                @if (isExpiringSoon(package.expiresOnDateUtc)) {
                <i class="pi pi-exclamation-triangle text-orange-500 ml-1"></i>
                }
              </div>
              } @else {
              <span class="text-muted">No Expiration</span>
              }
              }

              <!-- Created Date -->
              @case ('dateCreatedUtc') {
              @if (package.dateCreatedUtc) {
              <span>{{ formatDate(package.dateCreatedUtc) }}</span>
              }
              }

              <!-- Total Amount for Teacher -->
              @case ('totalAmountForTeacher') {
              <div class="amount-cell text-right">
                @if (package.totalAmountForTeacher) {
                <span class="font-semibold">{{ formatCurrency(package.totalAmountForTeacher) }}</span>
                } @else {
                <span class="text-muted">N/A</span>
                }
              </div>
              }

              <!-- Amount Paid to Teacher -->
              @case ('amountPaidToTeacher') {
              <div class="amount-cell text-right">
                @if (package.amountPaidToTeacher !== null && package.amountPaidToTeacher !== undefined) {
                <span class="font-semibold text-green-600">{{ formatCurrency(package.amountPaidToTeacher) }}</span>
                } @else {
                <span class="text-muted">€0.00</span>
                }
              </div>
              }

              <!-- Amount Remaining for Teacher -->
              @case ('amountRemainingForTeacher') {
              <div class="amount-cell text-right">
                @if (package.amountRemainingForTeacher !== null && package.amountRemainingForTeacher !== undefined) {
                <span class="font-semibold"
                  [ngClass]="package.amountRemainingForTeacher > 0 ? 'text-orange-600' : 'text-green-600'">
                  {{ formatCurrency(package.amountRemainingForTeacher) }}
                </span>
                } @else {
                <span class="text-muted">€0.00</span>
                }
              </div>
              }

              <!-- Parent Account Deleted -->
              @case ('parentAccountDeleted') {
              <div class="status-cell text-center">
                @if (package.parentAccountDeleted) {
                <span
                  class="inline-flex align-items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                  <i class="pi pi-times-circle mr-1"></i>
                  Deleted
                </span>
                } @else {
                No
                }
              </div>
              }

              <!-- Default case for other fields -->
              @default {
              <span>{{ getFieldValue(package, col.field) }}</span>
              }
              }
            </td>
            }
          </tr>
        </ng-template>

        <!-- Expansion Row Template -->
        <ng-template pTemplate="expandedrow" let-package>
          <tr>
            <td [attr.colspan]="selectedColumns().length + 1" class="p-0">
              <div class="expansion-content p-3 bg-gray-50 border-top-1 border-gray-200">

                <!-- Package Overview Section -->
                <div class="expansion-overview mb-3 p-3 bg-white border-round shadow-1">
                  <div class="flex align-items-center justify-content-between mb-2">
                    <h4 class="text-base font-semibold text-primary m-0">
                      <i class="pi pi-box mr-2"></i>Package Overview

                      
                  <div class="flex align-items-center gap-2">
                    <span class="package-type-badge" [ngClass]="'package-type-' + package.packageType">
                      {{ getPackageTypeText(package.packageType) }}
                    </span>
                    <span class="package-status-badge" [ngClass]="'package-status-' + package.packageStatus">
                      {{ getPackageStatusText(package.packageStatus) }}
                    </span>
                  </div>
                    </h4>
                  </div>

                  <div class="grid">


                    <div class="col-6 md:col-3">
                      <div class="info-card">
                        <div class="info-label">Package ID</div>
                        <div class="info-value font-mono">{{ package.id }}</div>
                      </div>
                    </div>
                    <div class="col-6 md:col-3">
                      <div class="info-card">
                        <div class="info-label">Language</div>
                        <div class="info-value">{{ package.teachingLanguageName }}</div>
                      </div>
                    </div>
                    <div class="col-6 md:col-3">
                      <div class="info-card">
                        <div class="info-label">Duration</div>
                        <div class="info-value">{{ package.durationInMinutes }} min</div>
                      </div>
                    </div>
                    <div class="col-6 md:col-3">
                      <div class="info-card">
                        <div class="info-label">Validity</div>
                        <div class="info-value">{{ package.validityInMonths }} months</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Progress & Dates Section -->
                <div class="expansion-progress mb-3 p-3 bg-white border-round shadow-1">
                  <h4 class="text-base font-semibold text-primary mb-2 m-0">
                    <i class="pi pi-chart-line mr-2"></i>Progress & Timeline
                  </h4>

                  <div class="grid">
                    <div class="col-12 md:col-6">
                      <div class="progress-section">
                        <div class="flex justify-content-between align-items-center mb-2">
                          <span class="text-sm font-medium">Lessons Progress</span>
                          <span class="text-sm font-semibold">{{ package.totalLessons - package.remainingLessons }}/{{
                            package.totalLessons }}</span>
                        </div>

                        <div class="text-xs text-muted">{{ getLessonsProgressPercentage(package) }}% completed</div>
                      </div>
                    </div>
                    <div class="col-12 md:col-6">
                      <div class="grid">
                        <div class="col-6">
                          <div class="info-card">
                            <div class="info-label">Created</div>
                            <div class="info-value text-sm">{{ formatDate(package.dateCreatedUtc) }}</div>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="info-card">
                            <div class="info-label">Expires</div>
                            <div class="info-value text-sm"
                              [ngClass]="{'text-orange-600': isExpiringSoon(package.expiresOnDateUtc), 'text-red-600': isExpired(package.expiresOnDateUtc)}">
                              {{ (package.expiresOnDateUtc) | date:'dd MMM yyyy HH:mm' }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Participants Section -->
                <div class="grid">
                  <!-- Parent Information -->
                  <div class="col-12 lg:col-4">
                    <div class="expansion-section p-3 bg-white border-round shadow-1 h-full">
                      <h4 class="text-base font-semibold mb-2 text-primary m-0">
                        <i class="pi pi-user mr-2"></i>Parent
                      </h4>
                      @if (package.parent) {
                      <div class="participant-card">
                        <div class="participant-name">{{ getFullName(package.parent) }}</div>
                        <div class="participant-details">
                         
                          <div class="detail-item">
                            <i class="pi pi-clock text-xs mr-1"></i>
                            <span>{{ getTimezone(package.parent) }}</span>
                          </div>
                          @if (package.parentAccountDeleted) {
                          <div class="detail-item text-red-600">
                            <i class="pi pi-exclamation-triangle text-xs mr-1"></i>
                            <span>Account Deleted</span>
                          </div>
                          }
                        </div>
                      </div>
                      } @else {
                      <div class="text-muted text-sm">No parent information</div>
                      }
                    </div>
                  </div>

                  <!-- Teacher Information -->
                  <div class="col-12 lg:col-4">
                    <div class="expansion-section p-3 bg-white border-round shadow-1 h-full">
                      <h4 class="text-base font-semibold mb-2 text-primary m-0">
                        <i class="pi pi-graduation-cap mr-2"></i>Teacher
                      </h4>
                      @if (package.teacher) {
                      <div class="participant-card">
                        <div class="participant-name">{{ getFullName(package.teacher) }}</div>
                        <div class="participant-details">
                          <div class="detail-item">
                            <i class="pi pi-envelope text-xs mr-1"></i>
                            <span>{{ enumDropdownOptionsService.getGenderDisplayText(package.teacher.gender) }}</span>
                          </div>
                          <div class="detail-item">
                            <i class="pi pi-clock text-xs mr-1"></i>
                            <span>{{ getTimezone(package.teacher) }}</span>
                          </div>
                          @if (package.teacher.profilePhotoUrl) {

                          }
                        </div>
                      </div>
                      } @else {
                      <div class="text-muted text-sm">No teacher assigned</div>
                      }
                    </div>
                  </div>

                  <!-- Students Information -->
                  <div class="col-12 lg:col-4">
                    <div class="expansion-section p-3 bg-white border-round shadow-1 h-full">
                      <h4 class="text-base font-semibold mb-2 text-primary m-0">
                        <i class="pi pi-users mr-2"></i>Students ({{ package.students?.length || 0 }})
                      </h4>
                      @if (package.students && package.students.length > 0) {
                      <div class="students-list">
                        @for (student of package.students; track student.userId) {
                        <div class="participant-card mb-2">
                          <div class="participant-name">{{ getFullName(student) }}</div>
                          <div class="participant-details">
                          
                            @if (getTimezone(student) !== 'N/A') {
                            <div class="detail-item">
                              <i class="pi pi-clock text-xs mr-1"></i>
                              <span>{{ getTimezone(student) }}</span>
                            </div>
                            }
                            @if (student.dateOfBirth) {
                            <div class="detail-item">
                              <i class="pi pi-calendar text-xs mr-1"></i>
                              <span>{{ generalService.calculateAge(student.dateOfBirth) + ' years old' }}</span>
                            </div>
                            }

                            @if (student.gender) {
                            <div class="detail-item">
                              <i class="pi pi-venus-mars text-xs mr-1"></i>
                              <span>{{ enumDropdownOptionsService.getGenderDisplayText(student.gender) }}</span>
                            </div>
                            }

                            @if (student.country) {
                            <div class="detail-item">
                              <i class="pi pi-flag text-xs mr-1"></i>
                              <span>{{ student.country }}</span>
                            </div>
                            }


                          </div>
                        </div>
                        }
                      </div>
                      } @else {
                      <div class="text-muted text-sm">No students enrolled</div>
                      }
                    </div>
                  </div>
                </div>

                <!-- Payment Information (if available) -->
               
                <div class="expansion-payment mt-3 p-3 bg-white border-round shadow-1">
                  <h4 class="text-base font-semibold text-primary mb-2 m-0">
                    <i class="pi pi-credit-card mr-2"></i>Teacher Payment Information
                  </h4>
                  <div class="grid">
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Total Amount</div>
                        <div class="info-value font-semibold">{{ formatCurrency(package.totalAmountForTeacher) }}</div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Amount Paid</div>
                        <div class="info-value font-semibold text-green-600">{{
                          formatCurrency(package.amountPaidToTeacher) }}</div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Remaining</div>
                        <div class="info-value font-semibold"
                          [ngClass]="(package.amountRemainingForTeacher || 0) > 0 ? 'text-orange-600' : 'text-green-600'">
                          {{ formatCurrency(package.amountRemainingForTeacher) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Extensions Information (if available) -->
                @if (package.hasBeenExtended || package.freeExtensionInMonths || package.addOnExtensionDurationInMonths)
                {
                <div class="expansion-extensions mt-3 p-3 bg-white border-round shadow-1">
                  <h4 class="text-base font-semibold text-primary mb-2 m-0">
                    <i class="pi pi-calendar-plus mr-2"></i>Extensions Information
                  </h4>
                  <div class="grid">
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Has Extensions</div>
                        <div class="info-value">
                          @if (package.hasBeenExtended) {
                          <span class="text-green-600"><i class="pi pi-check mr-1"></i>Yes</span>
                          } @else {
                          <span class="text-muted"><i class="pi pi-times mr-1"></i>No</span>
                          }
                        </div>
                      </div>
                    </div>
                    @if (package.freeExtensionInMonths) {
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Free Extension</div>
                        <div class="info-value">{{ package.freeExtensionInMonths }} months</div>
                      </div>
                    </div>
                    }
                    @if (package.addOnExtensionDurationInMonths) {
                    <div class="col-4">
                      <div class="info-card">
                        <div class="info-label">Add-on Extension</div>
                        <div class="info-value">{{ package.addOnExtensionDurationInMonths }} months</div>
                      </div>
                    </div>
                    }
                  </div>
                </div>
                }

              </div>
            </td>
          </tr>
        </ng-template>

        <!-- Table Footer -->
        <ng-template pTemplate="footer">
          <app-data-grid-header-footer [columns]="selectedColumns()" [sortable]="true" [reorderable]="true"
            [showHeader]="false" [showFooter]="true" [showActionsColumn]="false" [showExpansionColumn]="true">
          </app-data-grid-header-footer>
        </ng-template>

        <!-- Empty State -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="selectedColumns().length + 1" class="text-center py-4">
              @if (isLoading()) {
              <div class="loading-message">
                <i class="pi pi-spin pi-spinner mr-2"></i>
                Loading packages...
              </div>
              } @else {
              <div class="empty-state">
                <i class="pi pi-inbox text-4xl text-muted mb-3"></i>
                <h3 class="text-lg font-semibold mb-2">No packages found</h3>
                <p class="text-muted">
                  @if (hasActiveFilters()) {
                  Try adjusting your filters to see more results.
                  } @else {
                  No packages have been created yet.
                  }
                </p>
              </div>
              }
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>