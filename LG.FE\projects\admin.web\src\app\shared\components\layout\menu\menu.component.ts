import { ChangeDetectionStrategy, Component, computed, EventEmitter, inject, Input, Output, type OnInit } from '@angular/core';
import { MenuDataService } from '../../../../core/services/menu-data.service';
import { MenuItem } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { Router, RouterModule } from '@angular/router';
import { StyleClassModule } from 'primeng/styleclass';
import { AuthStateService, GeneralService } from 'SharedModules.Library';
import { CommonService } from '../../../services/common.service';

@Component({
  selector: 'app-menu',
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    MenuModule,
    StyleClassModule,
  ],
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuComponent implements OnInit {
  @Input() display!: boolean;
  @Output() sidebarToggleEvent = new EventEmitter<boolean>();
  menuDataService = inject(MenuDataService);
  authStateService = inject(AuthStateService);
  commonService = inject(CommonService);
  generalService = inject(GeneralService);
  router = inject(Router);
  visible = true;
  items: MenuItem[] = [];
  user$ = computed(() => this.authStateService.getUserClaims())

  menuStructure = {} as any;

  constructor() { }

  ngOnInit(): void {
    const menuItems = [
      {
        "label": "Overview",
        "icon": "pi pi-home",
        "routerLink": "/dashboard/overview"
      },
      {
        "label": "Teachers",
        "icon": "pi pi-briefcase",
        "items": [
          { "label": "Register New Teacher", "icon": "pi pi-folder", "routerLink": "/dashboard/teachers/add" },
          { "label": "Teachers list", "icon": "pi pi-folder", "routerLink": "/dashboard/teachers/list" },
          { "label": "Days off", "icon": "pi pi-folder", "routerLink": "/dashboard/teachers/days-off" }
        ]
      },
      {
        "label": "Users",
        "icon": "pi pi-chevron-down",
        "items": [
          { "label": "Parents", "icon": "pi pi-user", "routerLink": "/dashboard/teachers/add" },
          { "label": "Students", "icon": "pi pi-graduation-cap", "routerLink": "/dashboard/students" },
          { "label": "Groups", "icon": "pi pi-users", "routerLink": "/dashboard/groups/list" },
          { "label": "Teacher Change Requests", "icon": "pi pi-refresh", "routerLink": "/dashboard/teacher-change-requests/list" },
          // {
          //   "label": "Test nested",
          //   "icon": "pi pi-chart-line",
          //   "items": [
          //     {
          //       "label": "Test nested Test nested", "icon": "pi pi-chart-line", "items": [
          //         { "label": "View", "icon": "pi pi-table", "routerLink": "" },
          //         { "label": "Search", "icon": "pi pi-search", "routerLink": "" }
          //       ]
          //     },
          //     { "label": "Expenses", "icon": "pi pi-chart-line", "routerLink": "" }
          //   ]
          // },
          { "label": "Team", "icon": "pi pi-users", "routerLink": "/team" },
          { "label": "Messages", "icon": "pi pi-comments", "badge": "3", "routerLink": "/messages" },
          { "label": "Calendar", "icon": "pi pi-calendar", "routerLink": "/calendar" },
          { "label": "Settings", "icon": "pi pi-cog", "routerLink": "/settings" }
        ]
      },
      {
        "label": "General",
        "icon": "pi pi-chevron-down",
        "items": [
          { "label": "Teaching Languages", "icon": "pi pi-folder", "routerLink": "/dashboard/teaching-languages" },
          { "label": "Prices", "icon": "pi pi-folder", "routerLink": "/dashboard/prices" },
          { "label": "Performance", "icon": "pi pi-chart-bar", "routerLink": "/performance" },
          { "label": "Settings", "icon": "pi pi-cog", "routerLink": "/app-settings" }
        ]
      }
    ];


    // ✅ Apply filter only once at the root level
    this.menuStructure = this.filterValidRoutes(menuItems);
    console.log(this.menuStructure);
  }

  private extractPaths(route: any, basePath: string = ''): string[] {
    const path = basePath + (route.path ? '/' + route.path : '');

    // Recursively extract child paths
    const childPaths = route.children ? route.children.flatMap((child: any) => this.extractPaths(child, path)) : [];

    return [path, ...childPaths].filter(p => p !== ''); // Remove empty paths
  }

  // Function to get all available routes
  private getAvailableRoutes(): Set<string> {
    return new Set(this.router.config.flatMap(route => this.extractPaths(route)));
  }

  // Function to filter valid routes
  private filterValidRoutes(items: any[]): any[] {
    const availableRoutes = this.getAvailableRoutes();
    console.log(availableRoutes);
    return items
      .filter(item => !item.routerLink || availableRoutes.has(item.routerLink))
      .map(item => ({
        ...item,
        items: item.items ? this.filterValidRoutes(item.items) : undefined
      }));
  }


  toggleSidebar(value: boolean) {
    this.sidebarToggleEvent.emit(value);
  }

  collapsed = false;
  toggleMenuCollapse() {
    this.collapsed = !this.collapsed;
    this.generalService.miniLayoutSidebar.set(this.collapsed);
  }
}




