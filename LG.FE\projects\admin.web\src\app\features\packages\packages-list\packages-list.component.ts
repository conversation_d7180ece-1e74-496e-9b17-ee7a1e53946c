// ============================================================================
// PACKAGES LIST COMPONENT - Displays and manages package data in a data grid
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { takeUntil, combineLatest } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import moment from "moment-timezone";

// === PRIMENG IMPORTS ===
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from "primeng/button";
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { RippleModule } from 'primeng/ripple';
import { FormsModule } from '@angular/forms';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto,
  nameOf,
  IPackageTypeEnum,
  IPackageStatusEnum,
  EnumDropdownOptionsService,
  IEnumDropdownOptions,
  GeneralService,
  IDataGridFields,
  IBasedDataGridRequest,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  AppliedFiltersTagsComponent,
  IAppliedFilterTag,
  IFilterTagRemoveEvent,
  BaseDataGridComponent,
  IBaseDataGridConfig,
  IPackage,
  IBasicProfileInfoDto,
  IGetStudentsRequest,
  IGetStudentsResponse,
  ISearchStudentDto,
  IGetTeachersRequest,
  IGetTeachersResponse,
  ISearchTeacherDto,
  IGenderEnum,
  IStudents,
  ITeachers
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';
import { IPackagesFilterActionEvent, IPackagesFilterChangeEvent, PackagesListFiltersComponent } from './packages-list-filters/packages-list-filters.component';

// === SERVICE IMPORTS ===
import { AppliedFiltersAdapterService } from '../../../shared/services/applied-filters-adapter.service';
import { PackageListComponentHelperService } from './package-list-component-helper.service';


// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Packages List Component
 *
 * Displays packages in a data grid with:
 * - Pagination and sorting
 * - Advanced filtering (status, type, dates, teacher, student, etc.)
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-packages-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    RippleModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    PackagesListFiltersComponent,
    FiltersDrawerSidebarComponent
  ],
  templateUrl: './packages-list.component.html',
  styleUrls: ['./packages-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackagesListComponent extends BaseDataGridComponent<
  IGetPackagesRequest,
  IGetPackagesResponse
> implements OnInit, OnDestroy {

  private packageHelperService = inject(PackageListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private appliedFiltersAdapter = inject(AppliedFiltersAdapterService);
  protected override generalService = inject(GeneralService);
  protected override router = inject(Router);

  // ============================================================================
  // VIEW CHILD REFERENCES
  // ============================================================================

  @ViewChild('packagesFilters') packagesFiltersComponent?: PackagesListFiltersComponent;

  // ============================================================================
  // COMPONENT SIGNALS AND COMPUTED PROPERTIES
  // ============================================================================

  packageTypes = signal<IEnumDropdownOptions[]>([]);
  packageStatuses = signal<IEnumDropdownOptions[]>([]);

  // Expansion state
  expandedRows = signal<{[key: string]: boolean}>({});

  // Signals to track selected teacher and student for applied filters display
  selectedTeacherForFilters = signal<ISearchTeacherDto | null>(null);
  selectedStudentForFilters = signal<ISearchStudentDto | null>(null);

  // Signals to track current IDs for comparison
  private currentTeacherId = signal<string | null>(null);
  private currentStudentId = signal<string | null>(null);

  // Computed property for visible columns (excludes hidden columns)
  visibleColumns = computed(() => this.cols.filter((col) => !col.hide));

  // Computed property for packages data
  packagesForTable = computed(() => {
    return this.dataResponse()?.pageData || [];
  });

  // Computed property for filter state
  packagesFilterState = computed(() => ({
    queryParams: this.queryParams(),
    packageTypes: this.packageTypes(),
    packageStatuses: this.packageStatuses(),
    isFilterOpen: this.isFiltersDrawerVisible()
  }));

  // Filters drawer configuration
  filtersDrawerConfig: IFiltersDrawerConfig = {
    headerText: 'Packages List Filters',
    width: '450px',
  };

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    this.initializeComponent();
    this.loadEnumData();

    // Filter out hidden columns for initial selection (following teachers-list pattern)
    const visibleColumns = this.cols.filter(col => !col.hide);
    this.selectedColumns.set(visibleColumns);

    super.ngOnInit(); // Call parent ngOnInit to set up base functionality

    // Handle initial teacher and student ID from URL
    this.handleTeacherIdChange();
    this.handleStudentIdChange();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy(); // Call parent ngOnDestroy for cleanup
  }

  // ============================================================================
  // TEACHER AND STUDENT HANDLING METHODS
  // ============================================================================

  /**
   * Handle teacher ID changes and load teacher details when needed
   */
  private handleTeacherIdChange(teacherId?: string): void {
    const newTeacherId = teacherId || this.queryParams().teacherId;
    const previousTeacherId = this.currentTeacherId();

    // Normalize empty strings to null
    const normalizedNewId = newTeacherId || null;
    const normalizedPreviousId = previousTeacherId || null;

    // Update the current teacher ID tracker
    this.currentTeacherId.set(normalizedNewId);

    // If teacher ID changed, clear the selected teacher
    if (normalizedPreviousId !== normalizedNewId) {
      this.selectedTeacherForFilters.set(null);

      // Load teacher details if we have a new ID
      if (normalizedNewId) {
        this.loadTeacherById(normalizedNewId);
      }
    }
  }

  /**
   * Handle student ID changes and load student details when needed
   */
  private handleStudentIdChange(studentId?: string): void {
    const newStudentId = studentId || this.queryParams().studentId;
    const previousStudentId = this.currentStudentId();

    // Normalize empty strings to null
    const normalizedNewId = newStudentId || null;
    const normalizedPreviousId = previousStudentId || null;

    // Update the current student ID tracker
    this.currentStudentId.set(normalizedNewId);

    // If student ID changed, clear the selected student
    if (normalizedPreviousId !== normalizedNewId) {
      this.selectedStudentForFilters.set(null);

      // Load student details if we have a new ID
      if (normalizedNewId) {
        this.loadStudentById(normalizedNewId);
      }
    }
  }

  /**
   * Load teacher details by ID using the teachers API
   */
  private loadTeacherById(teacherId: string): void {
    const request: IGetTeachersRequest = {
      searchTerm: teacherId,
      pageNumber: 1,
      pageSize: 1,
      gender: IGenderEnum.None,
      studentAgesMin: 2,
      studentAgesMax: 17
    };

    this.handleApiService.getApiData<IGetTeachersResponse>({
      url: ITeachers.getTeachers,
      method: 'GET'
    }, request).subscribe({
      next: (response) => {
        if (response?.pageData?.length > 0) {
          const teacher = response.pageData[0];
          this.selectedTeacherForFilters.set(teacher);
        } else {
          console.log('No teacher found with ID:', teacherId);
          this.cleanupInvalidTeacherId(teacherId);
        }
      },
      error: (error) => {
        console.error('Failed to load teacher details:', error);
        this.cleanupInvalidTeacherId(teacherId);
      }
    });
  }

  /**
   * Load student details by ID using the students API
   */
  private loadStudentById(studentId: string): void {
    const request: IGetStudentsRequest = {
      searchTerm: studentId,
      pageNumber: 1,
      pageSize: 1,
      gender: IGenderEnum.None,
      studentAgesMin: 0,
      studentAgesMax: 17
    };

    this.handleApiService.getApiData<IGetStudentsResponse>({
      url: IStudents.getStudents,
      method: 'GET'
    }, request).subscribe({
      next: (response) => {
        if (response?.pageData?.length > 0) {
          const student = response.pageData[0];
          this.selectedStudentForFilters.set(student);
        } else {
          console.log('No student found with ID:', studentId);
          this.cleanupInvalidStudentId(studentId);
        }
      },
      error: (error) => {
        console.error('Failed to load student details:', error);
        this.cleanupInvalidStudentId(studentId);
      }
    });
  }

  /**
   * Clean up invalid teacher ID from URL params and filters
   */
  private cleanupInvalidTeacherId(invalidTeacherId: string): void {
    this.selectedTeacherForFilters.set(null);
    this.currentTeacherId.set(null);
    this.cleanupInvalidFilterParam('teacherId', `Cleaning up invalid teacher ID: ${invalidTeacherId}`);
  }

  /**
   * Clean up invalid student ID from URL params and filters
   */
  private cleanupInvalidStudentId(invalidStudentId: string): void {
    this.selectedStudentForFilters.set(null);
    this.currentStudentId.set(null);
    this.cleanupInvalidFilterParam('studentId', `Cleaning up invalid student ID: ${invalidStudentId}`);
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ============================================================================

  override getConfig(): IBaseDataGridConfig<IGetPackagesRequest> {
    return {
      defaultRequest: this.packageHelperService.createDefaultRequest(),
      apiEndpoint: IPackage.getPackages,
      errorPrefix: 'Failed to load packages',
      mapUrlParamsToRequest: (params: Params) =>
        this.packageHelperService.mapQueryParamsToRequest(params),
      createDefaultRequest: () =>
        this.packageHelperService.createDefaultRequest(),
      appliedFiltersConfig: {
        convertToFilterTags: (request, urlParams) => this.convertToFilterTags(request, urlParams),
        getFiltersCount: (filters) => filters.length
      },
      fieldNames: this.packageHelperService.getFieldNames(),
    };
  }

  // ============================================================================
  // INITIALIZATION METHODS
  // ============================================================================

  private initializeComponent(): void {
    // Initialize table columns
    this.cols = this.packageHelperService.initializeTableColumns();
  }

  private loadEnumData(): void {
    // Load package types manually
    this.packageTypes.set([
      { value: IPackageTypeEnum.FreeTrial, label: 'Free Trial' },
      { value: IPackageTypeEnum.Paid, label: 'Paid' },
      { value: IPackageTypeEnum.Gift, label: 'Gift' }
    ]);

    // Load package statuses manually
    this.packageStatuses.set([
      { value: IPackageStatusEnum.Active, label: 'Active' },
      { value: IPackageStatusEnum.InActive, label: 'Inactive' },
      { value: IPackageStatusEnum.Expired, label: 'Expired' },
      { value: IPackageStatusEnum.Completed, label: 'Completed' },
      { value: IPackageStatusEnum.Refunded, label: 'Refunded' }
    ]);
  }

  // ============================================================================
  // APPLIED FILTERS METHODS
  // ============================================================================

  private convertToFilterTags(request: IGetPackagesRequest, _urlParams: any): IAppliedFilterTag[] {
    const filters: IAppliedFilterTag[] = [];
    const fieldNames = nameOf<IGetPackagesRequest>();

    // Sort column filter (show if different from default)
    const defaultSortColumn = this.packageHelperService.getDefaultSortColumn();
    const defaultSortDirection = this.packageHelperService.getDefaultSortDirection();

    if (request.sortColumn &&
        (request.sortColumn !== defaultSortColumn || request.sortDirection !== defaultSortDirection)) {
      const sortDisplayName = this.getSortColumnDisplayName(request.sortColumn);
      const directionText = request.sortDirection === 'asc' ? 'Ascending' : 'Descending';

      filters.push({
        id: fieldNames.sortColumn!,
        label: `Sort: ${sortDisplayName} (${directionText})`,
        icon: 'pi pi-sort-alt',
        type: 'sort',
        removeData: { filterName: fieldNames.sortColumn! }
      });
    }

    // Search term filter
    if (request.searchTerm) {
      filters.push({
        id: fieldNames.searchTerm!,
        label: `Search: ${request.searchTerm}`,
        icon: 'pi pi-search',
        type: 'search',
        removeData: { filterName: fieldNames.searchTerm! }
      });
    }

    // Package type filter
    if (request.packageType !== null && request.packageType !== undefined) {
      const typeText = this.getPackageTypeText(request.packageType);
      filters.push({
        id: fieldNames.packageType!,
        label: `Type: ${typeText}`,
        icon: 'pi pi-tag',
        type: 'select',
        removeData: { filterName: fieldNames.packageType! }
      });
    }

    // Package status filter
    if (request.packageStatus !== null && request.packageStatus !== undefined) {
      const statusText = this.getPackageStatusText(request.packageStatus);
      filters.push({
        id: fieldNames.packageStatus!,
        label: `Status: ${statusText}`,
        icon: 'pi pi-info-circle',
        type: 'select',
        removeData: { filterName: fieldNames.packageStatus! }
      });
    }

    // Date filters
    if (request.purchasedFrom) {
      filters.push({
        id: fieldNames.purchasedFrom!,
        label: `Purchased From: ${this.formatDate(request.purchasedFrom)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames.purchasedFrom! }
      });
    }

    if (request.purchasedTo) {
      filters.push({
        id: fieldNames.purchasedTo!,
        label: `Purchased To: ${this.formatDate(request.purchasedTo)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames.purchasedTo! }
      });
    }

    // Teacher filter
    if (request.teacherId) {
      const selectedTeacher = this.selectedTeacherForFilters();
      const teacherLabel = selectedTeacher && selectedTeacher.id === request.teacherId
        ? `Teacher: ${selectedTeacher.firstName} ${selectedTeacher.lastName}`
        : `Teacher ID: ${request.teacherId}`;

      filters.push({
        id: fieldNames.teacherId!,
        label: teacherLabel,
        icon: 'pi pi-user',
        type: 'select',
        removeData: { filterName: fieldNames.teacherId! }
      });
    }

    // Student filter
    if (request.studentId) {
      const selectedStudent = this.selectedStudentForFilters();
      const studentLabel = selectedStudent && selectedStudent.userId === request.studentId
        ? `Student: ${selectedStudent.firstName} ${selectedStudent.lastName}`
        : `Student ID: ${request.studentId}`;

      filters.push({
        id: fieldNames.studentId!,
        label: studentLabel,
        icon: 'pi pi-graduation-cap',
        type: 'select',
        removeData: { filterName: fieldNames.studentId! }
      });
    }

    // Group filter
    if (request.groupId) {
      filters.push({
        id: fieldNames.groupId!,
        label: `Group: ${request.groupId}`, // TODO: Resolve group name
        icon: 'pi pi-users',
        type: 'select',
        removeData: { filterName: fieldNames.groupId! }
      });
    }

    // Teaching Language filter
    if (request.teachingLanguageId) {
      filters.push({
        id: fieldNames.teachingLanguageId!,
        label: `Teaching Language: ${request.teachingLanguageId}`, // TODO: Resolve language name
        icon: 'pi pi-globe',
        type: 'select',
        removeData: { filterName: fieldNames.teachingLanguageId! }
      });
    }

    // Parent filter
    if (request.parentId) {
      filters.push({
        id: fieldNames.parentId!,
        label: `Parent: ${request.parentId}`, // TODO: Resolve parent name
        icon: 'pi pi-user',
        type: 'select',
        removeData: { filterName: fieldNames.parentId! }
      });
    }

    // Expiration date filters
    if (request.expiresFrom) {
      filters.push({
        id: fieldNames.expiresFrom!,
        label: `Expires From: ${this.formatDate(request.expiresFrom)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames.expiresFrom! }
      });
    }

    if (request.expiresTo) {
      filters.push({
        id: fieldNames.expiresTo!,
        label: `Expires To: ${this.formatDate(request.expiresTo)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames.expiresTo! }
      });
    }

    // Boolean filters
    if (request.hasAddOnExtension !== null && request.hasAddOnExtension !== undefined) {
      const extensionText = request.hasAddOnExtension ? 'Yes' : 'No';
      filters.push({
        id: fieldNames.hasAddOnExtension!,
        label: `Has Add-on Extension: ${extensionText}`,
        icon: 'pi pi-plus-circle',
        type: 'boolean',
        removeData: { filterName: fieldNames.hasAddOnExtension! }
      });
    }

    if (request.parentAccountDeleted !== null && request.parentAccountDeleted !== undefined) {
      const deletedText = request.parentAccountDeleted ? 'Yes' : 'No';
      filters.push({
        id: fieldNames.parentAccountDeleted!,
        label: `Parent Account Deleted: ${deletedText}`,
        icon: 'pi pi-trash',
        type: 'boolean',
        removeData: { filterName: fieldNames.parentAccountDeleted! }
      });
    }

    if (request.hasOutstandingTeacherPayments) {
      filters.push({
        id: fieldNames.hasOutstandingTeacherPayments!,
        label: `Has Outstanding Teacher Payments: Yes`,
        icon: 'pi pi-dollar',
        type: 'boolean',
        removeData: { filterName: fieldNames.hasOutstandingTeacherPayments! }
      });
    }

    return filters;
  }

  // ============================================================================
  // FILTER METHODS
  // ============================================================================

  onFiltersChange(event: IPackagesFilterChangeEvent): void {
    // Handle individual filter changes - this is for real-time updates
    // For now, we'll just log it since we're using the drawer pattern
    console.debug('Filter change detected:', event.filterName, event.value);
  }

  onFiltersAction(event: IPackagesFilterActionEvent): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  /**
   * Handle filters drawer action events
   */
  override onFiltersDrawerAction(event: any): void {
    switch (event.action) {
      case 'apply':
        // Apply temporary filters from the filters component
        if (this.packagesFiltersComponent) {
          const filters = this.packagesFiltersComponent.getCurrentFilters();
          this.onFiltersAction({ action: 'search', filters });
        }
        this.closeFiltersDrawer();
        break;

      case 'reset':
        // Reset only clears temporary filter state, doesn't apply the reset
        // The reset will only be applied when user clicks "Apply Filters"
        if (this.packagesFiltersComponent) {
          this.packagesFiltersComponent.resetFiltersToDefault();
        }
        // Do NOT close the drawer - user should be able to see the reset state
        // and decide whether to apply it or make further changes
        break;

      case 'close':
        // Check if there are pending changes and handle accordingly
        if (this.packagesFiltersComponent?.hasPendingChanges()) {
          // Discard temporary changes when closing without applying
          this.packagesFiltersComponent.discardTempChanges();
        }
        this.closeFiltersDrawer();
        break;

      default:
        console.warn('Unknown filter drawer action:', event.action);
    }
  }

  override closeFiltersDrawer(): void {
    this.isFiltersDrawerVisible.set(false);
  }

  override resetFilters(): void {
    this.saveScrollPosition();

    // Reset to default values using helper service
    this.queryParams.set(this.packageHelperService.createDefaultRequest());

    // No need to manually reset UI state - computed signals will automatically update when queryParams change

    this.generalService.updateQueryParams(this.queryParams(), {
      preserveFragment: true,
      queryParamsHandling: '',
      replaceUrl: true
    });

    // BaseDataGrid will automatically fetch data when URL changes
    this.restoreScrollPosition();
  }

  // Override the base class method to handle date formatting for API calls
  protected override cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    return this.packageHelperService.cleanRequestForApi(request as unknown as IGetPackagesRequest) as Partial<T>;
  }

  override removeFilter(filterName: string, event?: MouseEvent): void {
    if (event) event.stopPropagation();

    const fieldNames = nameOf<IGetPackagesRequest>();

    // Handle special filter removals
    if (filterName === fieldNames.sortColumn!) {
      // When removing sort filter, reset to default sort
      this.queryParams.update(current => ({
        ...current,
        sortColumn: this.packageHelperService.getDefaultSortColumn(),
        sortDirection: this.packageHelperService.getDefaultSortDirection(),
        pageNumber: 1
      }));
    } else {
      // Handle teacher and student filter removals
      if (filterName === fieldNames.teacherId!) {
        this.selectedTeacherForFilters.set(null);
        this.currentTeacherId.set(null);
      } else if (filterName === fieldNames.studentId!) {
        this.selectedStudentForFilters.set(null);
        this.currentStudentId.set(null);
      }

      // Standard filter removal
      this.queryParams.update(current => {
        const updated = { ...current };
        delete updated[filterName as keyof IGetPackagesRequest];
        updated.pageNumber = 1; // Reset to first page when removing filters
        return updated;
      });
    }

    // Update URL and reload data
    this.generalService.updateQueryParams(this.queryParams(), {
      replaceUrl: true,
    });
  }

  /**
   * Handle applied filter removal
   */
  override onAppliedFilterRemove(event: IFilterTagRemoveEvent): void {
    console.log('onAppliedFilterRemove', event);
    const filterName = event.filter.removeData?.filterName;
    if (filterName) {
      this.removeFilter(filterName, event.event);
    }
  }

  /**
   * Handle clear all applied filters
   */
  override onAppliedFiltersClearAll(_event: MouseEvent): void {
    // Clear teacher and student selections
    this.selectedTeacherForFilters.set(null);
    this.currentTeacherId.set(null);
    this.selectedStudentForFilters.set(null);
    this.currentStudentId.set(null);

    this.resetFilters();
  }





  // ============================================================================
  // TABLE METHODS
  // ============================================================================

  onExport(): void {
    if (this.table && this.dataResponse() && this.selectedColumns()) {
      this.packageHelperService.exportTable(this.table, this.selectedColumns(), this.dataResponse()!);
    }
  }

  /**
   * Handle column selection changes
   */
  override onColumnsChange(selectedColumns: IDataGridFields[]): void {
    this.selectedColumns.set(selectedColumns);
  }

  /**
   * Handle column reorder event
   */
  onColumnReorder(event: { columns?: IDataGridFields[] }): void {
    if (event.columns?.length) {
      const reorderedColumns = event.columns.filter(
        (col: IDataGridFields) => col?.field && col?.header
      ) as IDataGridFields[];
      this.selectedColumns.set([...reorderedColumns]);
    }
  }

  /**
   * Update search term from input event
   */
  override updateSearchTerm(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChange(target.value);
  }

  /**
   * Clear search term
   */
  override clearSearchTerm(): void {
    this.onSearchChange('');
  }

  /**
   * Handle search input changes
   */
  override onSearchChange(searchTerm: string): void {
    const currentParams = { ...this.queryParams() };
    currentParams.searchTerm = searchTerm || null;
    currentParams.pageNumber = 1; // Reset to first page when searching

    this.queryParams.set(currentParams);
    this.updateUrlParams();
  }

  /**
   * Export packages to Excel
   */
  exportToExcel(): void {
    const response = this.dataResponse();
    if (response) {
      this.packageHelperService.exportTable(
        this.table,
        this.selectedColumns(),
        response
      );
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  protected getEntityDisplayName(): string {
    return 'Packages';
  }

  protected getCreateRoute(): string {
    return '/admin/packages/create';
  }

  /**
   * Get display name for sort column
   */
  getSortColumnDisplayName(sortColumn?: string): string {
    const columnToCheck = sortColumn || this.queryParams().sortColumn;
    const column = this.cols.find(col => col.field === columnToCheck);
    return column ? column.header : columnToCheck || 'Unknown';
  }

  /**
   * Get display name for sort column from URL parameters
   */
  getSortColumnDisplayNameFromUrl(): string {
    const fieldNames = nameOf<IGetPackagesRequest>();
    const sortColumn = this.currentUrlParams()[fieldNames.sortColumn!];
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  // ============================================================================
  // TEMPLATE HELPER METHODS
  // ============================================================================

  getPackageTypeText(packageType: IPackageTypeEnum): string {
    return this.generalService.getEnumDisplayText(packageType, IPackageTypeEnum);
  }

  getPackageStatusText(packageStatus: IPackageStatusEnum): string {
    return this.generalService.getEnumDisplayText(packageStatus, IPackageStatusEnum);
  }

  getStudentNames(students: IBasicProfileInfoDto[]): string {
    if (!students || students.length === 0) return '';
    return students.map(s => `${s.firstName} ${s.lastName}`).join(', ');
  }

  getExpirationClass(expirationDate: Date | string | null): string {
    if (!expirationDate) return '';

    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');

    if (daysUntilExpiration < 0) {
      return 'text-red-500'; // Expired
    } else if (daysUntilExpiration <= 7) {
      return 'text-orange-500'; // Expiring soon
    } else if (daysUntilExpiration <= 30) {
      return 'text-yellow-500'; // Expiring within a month
    }
    return 'text-green-500'; // Good expiration date
  }

  isExpiringSoon(expirationDate: Date | string | null): boolean {
    if (!expirationDate) return false;

    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');

    return daysUntilExpiration >= 0 && daysUntilExpiration <= 7;
  }

  isExpired(expirationDate: Date | string | null): boolean {
    if (!expirationDate) return false;

    const expDate = moment(expirationDate);
    const now = moment();

    return expDate.isBefore(now);
  }

  getLessonsProgressPercentage(packageItem: ISearchPackageDto): number {
    if (packageItem.totalLessons === 0) return 0;
    const completed = packageItem.totalLessons - packageItem.remainingLessons;
    return Math.round((completed / packageItem.totalLessons) * 100);
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    try {
      return moment(date).format('DD/MM/YYYY');
    } catch {
      return '';
    }
  }

  formatCurrency(amount: number | null | undefined): string {
    if (amount === null || amount === undefined) return '€0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  getFieldValue(item: any, field: string): string {
    const value = item[field];
    if (value === null || value === undefined) return '';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  }

  hasActiveFilters(): boolean {
    return this.appliedFilters().length > 0;
  }

  // ============================================================================
  // EXPANSION METHODS
  // ============================================================================

  /**
   * Toggle expansion state for a package row
   */
  toggleExpansion(packageId: string): void {
    this.expandedRows.update(current => ({
      ...current,
      [packageId]: !current[packageId]
    }));
  }

  /**
   * Check if a package row is expanded
   */
  isExpanded(packageId: string): boolean {
    return this.expandedRows()[packageId] || false;
  }

  /**
   * Get formatted full name from IBasicProfileInfoDto
   */
  getFullName(profile: IBasicProfileInfoDto | null | undefined): string {
    if (!profile) return 'N/A';
    return `${profile.firstName} ${profile.lastName}`.trim();
  }

  /**
   * Get formatted timezone from IBasicProfileInfoDto
   */
  getTimezone(profile: IBasicProfileInfoDto | null | undefined): string {
    return profile?.timeZoneDisplayName || profile?.timeZoneIana || 'N/A';
  }

  onCreateClick(): void {
    this.router.navigate([this.getCreateRoute()]);
  }
}
