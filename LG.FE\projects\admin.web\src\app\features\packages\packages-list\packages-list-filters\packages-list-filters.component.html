<!-- Packages List Filters Content Only -->
<div class="filters-content">
  <div class="grid">

    <!-- Basic Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
     

    <!-- Package Type Filter -->
        <div class="field mb-0">
          <label for="packageType" class="block text-sm mb-2">Package Type</label>
          <p-select
            [appendTo]="'body'"
            id="packageType"
            [options]="packageTypes()"
            [ngModel]="currentFilters().packageType"
            (ngModelChange)="onFilterChange('packageType', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Package Type"
            class="w-full">
          </p-select>
        </div>

        <!-- Package Status Filter -->
        <div class="field mb-0">
          <label for="packageStatus" class="block text-sm mb-2">Package Status</label>
          <p-select
            [appendTo]="'body'"
            id="packageStatus"
            [options]="packageStatuses()"
            [ngModel]="currentFilters().packageStatus"
            (ngModelChange)="onFilterChange('packageStatus', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Package Status"
            class="w-full">
          </p-select>
        </div>
      </div>
    </div>

    <!-- Selection Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <!-- Teacher Selection -->
        <div class="field mb-0">
          <label for="teacher" class="block text-sm mb-2">Teacher</label>
          <lib-prime-teachers-selection
            #teachersSelection
            [pageSize]="20"
            [enablePagination]="true"
            [enableSearch]="true"
            [autoLoadInitialData]="false"
            [searchPlaceholder]="'Search teacher...'"
            [initialSelectedId]="currentFilters().teacherId!"
            [enableInitialItemSelection]="true"
            [resetSelectionSignal]="resetSelectionSignal()"
            (itemClicked)="onTeacherSelected($event)">
          </lib-prime-teachers-selection>
        </div>

        <!-- Student Selection -->
        <div class="field mb-0">
          <label for="student" class="block text-sm mb-2">Student</label>
          <lib-prime-students-selection
            #studentsSelection
            [pageSize]="20"
            [enablePagination]="true"
            [enableSearch]="true"
            [autoLoadInitialData]="false"
            [searchPlaceholder]="'Search student...'"
            [initialSelectedId]="currentFilters().studentId!"
            [enableInitialItemSelection]="true"
            [resetSelectionSignal]="resetSelectionSignal()"
            (itemClicked)="onStudentSelected($event)">
          </lib-prime-students-selection>
        </div>
      </div>
    </div>

    <!-- Date Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <h4 class="text-sm font-semibold mt-0 mb-2">Date Filters</h4>

        <!-- Purchased Date Range -->
        <div class="field mb-0">
          <div class="flex flex-column gap-2 align-items-start">
          <label class="block text-sm">Purchased Date From</label>
            <p-datepicker
            [appendTo]="'body'"
              [ngModel]="currentFilters().purchasedFrom"
              (ngModelChange)="onPurchasedFromChange($event)"
              placeholder="From date"
              [showIcon]="true"
              [showClear]="true"
              class="w-full"
              styleClass="w-full">
            </p-datepicker>
          <label class="block text-sm">Purchased Date From</label>
            <p-datepicker
            [appendTo]="'body'"
              [ngModel]="currentFilters().purchasedTo"
              (ngModelChange)="onPurchasedToChange($event)"
              placeholder="To date"
              [showIcon]="true"
              [showClear]="true"
              class="w-full"
              styleClass="w-full">
            </p-datepicker>
          </div>
        </div>

        <!-- Expiration Date Range -->
        <div class="field mb-0">
          <div class="flex flex-column gap-2 align-items-start">
          <label class="block text-sm">Expiration Date From</label>
            <p-datepicker
            [appendTo]="'body'"
              [ngModel]="currentFilters().expiresFrom"
              (ngModelChange)="onExpiresFromChange($event)"
              placeholder="Expiration From date"
              [showIcon]="true"
              [showClear]="true"
              class="w-full"
              styleClass="w-full">
            </p-datepicker>
          <label class="block text-sm">Expiration Date To</label>
            <p-datepicker
            [appendTo]="'body'"
              [ngModel]="currentFilters().expiresTo"
              (ngModelChange)="onExpiresToChange($event)"
              placeholder="Expiration To date"
              [showIcon]="true"
              [showClear]="true"
              class="w-full"
              styleClass="w-full">
            </p-datepicker>
          </div>
        </div>
      </div>
    </div>

    <!-- Boolean Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <h4 class="text-sm font-semibold mt-0 mb-2">Additional Filters</h4>

        <!-- Has Add-on Extension -->
        <div class="field mb-0">
          <label for="hasAddOnExtension" class="block text-sm mb-2">Has Add-on Extension</label>
          <p-select
            [appendTo]="'body'"
            id="hasAddOnExtension"
            [options]="getBooleanOptions()"
            [ngModel]="currentFilters().hasAddOnExtension"
            (ngModelChange)="onFilterChange('hasAddOnExtension', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select option"
            class="w-full">
          </p-select>
        </div>

        <!-- Parent Account Deleted -->
        <div class="field mb-0">
          <label for="parentAccountDeleted" class="block text-sm mb-2">Parent Account Deleted</label>
          <p-select
            [appendTo]="'body'"
            id="parentAccountDeleted"
            [options]="getBooleanOptions()"
            [ngModel]="currentFilters().parentAccountDeleted"
            (ngModelChange)="onFilterChange('parentAccountDeleted', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select option"
            class="w-full">
          </p-select>
        </div>

        <!-- Outstanding Teacher Payments -->
        <div class="field mt-3 mb-3">
          <div class="surface-card border-round p-3 shadow-1">
            <div class="flex align-items-center gap-3">
              <div class="flex-1">
                <div class="flex align-items-center gap-2">
                  <p-checkbox
                    [ngModel]="currentFilters().hasOutstandingTeacherPayments"
                    (ngModelChange)="onFilterChange('hasOutstandingTeacherPayments', $event)"
                    [binary]="true"
                    inputId="outstandingPayments">
                  </p-checkbox>
                  <label for="outstandingPayments" class="text-sm font-medium text-900 cursor-pointer">
                    Has Outstanding Teacher Payments
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Is Teacher Assignment Page -->
        <div class="field mb-3">
          <div class="surface-card border-round p-3 shadow-1">
            <div class="flex align-items-center gap-3">
              <div class="flex-1">
                <div class="flex align-items-center gap-2">
                  <p-checkbox
                    [ngModel]="currentFilters().isTeacherAssigmentPage"
                    (ngModelChange)="onFilterChange('isTeacherAssigmentPage', $event)"
                    [binary]="true"
                    inputId="isTeacherAssigmentPage">
                  </p-checkbox>
                  <label for="isTeacherAssigmentPage" class="text-sm font-medium text-900 cursor-pointer">
                    Is Teacher Assignment Page
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
