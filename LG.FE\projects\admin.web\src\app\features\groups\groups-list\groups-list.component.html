<!-- groups-list.component.html -->
<div class="groups-page-container">
  <!-- Main Content (Table & Applied Filters) -->
  <div class="main-content">
    <!-- Applied Filters Section - New Reusable Component -->
    <app-applied-filters-tags
      [filters]="appliedFilters()"
      (filterRemoved)="onAppliedFilterRemove($event)"
      (clearAllClicked)="onAppliedFiltersClearAll($event)">
           <p-button extraButton
                icon="pi pi-filter"
                label="Filters"
                severity="secondary"
                (click)="openFiltersDrawer()"
                class=""
                styleClass="p-button-sm"
                [badge]="appliedFiltersCount() > 0 ? appliedFiltersCount().toString() : undefined"
                badgeClass="p-badge-info" />
    </app-applied-filters-tags>

    <!-- Filters Drawer Sidebar -->
    <app-filters-drawer-sidebar
      #filtersDrawer
      [(visible)]="isFiltersDrawerVisible"
      [config]="filtersDrawerConfig"
      [filterContentTemplate]="filterContentTemplate"
      (actionClicked)="onFiltersDrawerAction($event)">
    </app-filters-drawer-sidebar>

    <!-- Filter Content Template -->
    <ng-template #filterContentTemplate>
      <app-groups-list-filters
        #groupsFilters
        [filterState]="filterState()"
        [preSelectedStudent]="selectedStudentForFilters()"
        [preSelectedTeacher]="selectedTeacherForFilters()"
        (filterChanged)="onFilterChange($event)"
        (filterAction)="onFilterAction($event)">
      </app-groups-list-filters>
    </ng-template>

    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
      <p-table #dt [value]="groupsForTable()" dataKey="id" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [first]="(queryParams().pageNumber - 1) * queryParams().pageSize"
        [totalRecords]="totalRecords()" [loading]="isLoading()"
        [rowsPerPageOptions]="rowsPerPageOptions()" [showCurrentPageReport]="true"
        [sortField]="queryParams().sortColumn" [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
        [reorderableColumns]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} groups"
        (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" (onColReorder)="onColumnReorder($event)"
        [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows
        [tableStyle]="{'min-width': '1000px; min-height: calc(100% + 300px)'}" [columns]="selectedColumns()"
        [expandedRowKeys]="expandedRows()" (onRowExpand)="onRowExpand($event)" (onRowCollapse)="onRowCollapse($event)">

        <ng-template pTemplate="caption">
          <div class="flex flex-column md:flex-row align-items-center md:justify-content-between gap-3 p-0">
            <!-- Search Row - Full width on all screens -->
            <div class="flex align-items-center">
              <div class="field flex-1 m-0">
                <div class="search-input-container">
                  <input type="text" pInputText
                    id="searchTerm"
                    type="text"
                    styleClass="w-full"
                    [value]="queryParams().searchTerm || ''"
                    (input)="updateSearchTerm($event)"
                    placeholder="Search groups" />
                  <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                    (click)="clearSearchTerm()">
                    <i class="pi pi-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Actions Row - Responsive layout -->
            <div class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center justify-content-between gap-3">
              <!-- Expand/Collapse Buttons - Full width on mobile, auto on desktop -->
              <div class="flex gap-2 justify-content-center sm:justify-content-start">
                <p-button
                  label="Expand All"
                  icon="pi pi-plus"
                  text
                  (onClick)="expandAll()"
                  class="p-button-sm flex-1 sm:flex-none" />
                <p-button
                  label="Collapse All"
                  icon="pi pi-minus"
                  text
                  (onClick)="collapseAll()"
                  class="p-button-sm flex-1 sm:flex-none" />
              </div>

              <!-- Column Selection and Export - Stack on mobile, inline on desktop -->
              <div class="flex flex-column sm:flex-row align-items-stretch sm:align-items-center gap-2 sm:gap-3">
                <p-multiSelect
                  [options]="visibleColumns()"
                  [ngModel]="selectedColumns()"
                  (ngModelChange)="onColumnsChange($event)"
                  [filter]="true"
                  optionLabel="header"
                  placeholder="Choose Columns"
                  [selectedItemsLabel]="'{0}/' + visibleColumns().length + ' columns shown'"
                  scrollHeight="400px"
                  class="w-full sm:w-auto" />
                <p-button
                  icon="pi pi-download"
                  label="Export"
                  severity="secondary"
                  (click)="exportTable()"
                  class="p-button-sm w-full sm:w-auto" />
              </div>
            </div>
          </div>
        </ng-template>

        <ng-template pTemplate="header">
          <!-- Table Header -->
          <app-data-grid-header-footer
            [columns]="selectedColumns()"
            [sortable]="true"
            [reorderable]="true"
            [showHeader]="true"
            [showFooter]="false"
            [showActionsColumn]="true"
            [showExpansionColumn]="true"
            [expansionColumnWidth]="'3rem'"
            actionsColumnHeader="Actions"
            actionsColumnWidth="100px">
          </app-data-grid-header-footer>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-group let-expanded="expanded">
          <tr>

            <!-- Expand Button Column (Frozen Left) -->
            <td alignFrozen="left" pFrozenColumn>
              <p-button
                type="button"
                pRipple
                [pRowToggler]="group"
                [text]="true"
                [rounded]="true"
                [plain]="true"
                [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                [disabled]="!hasStudents(group)"
                pTooltip="Expand to view students"
                tooltipPosition="top">
              </p-button>
            </td>

            <!-- Actions Column (Frozen Left) -->
            <td alignFrozen="left" pFrozenColumn>
              <div class="flex justify-content-center">
                <button (click)="onSelectListGroupItem(group.id, group.parentId)" pButton icon="pi pi-eye" class="p-button-text p-button-sm"></button>
              </div>
            </td>

            <td *ngFor="let col of selectedColumns()">
              <ng-container [ngSwitch]="col.field">

                <!-- Group Name -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.groupName">
                  <div class="font-semibold text-900"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.groupName }}
                  </div>
                </ng-container>

                <!-- Teaching Language -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.teachingLanguageName">
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.teachingLanguageName }}
                  </div>
                </ng-container>

                <!-- Group Level -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.groupLevel">
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <p-tag
                      [value]="formatLanguageLevel(group.groupLevel)"
                      severity="info">
                    </p-tag>
                  </div>
                </ng-container>

                <!-- Group Status -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.groupStatus">
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <p-tag
                      [value]="formatGroupStatus(group.groupStatus)"
                      [severity]="getGroupStatusSeverity(group.groupStatus)">
                    </p-tag>
                  </div>
                </ng-container>

                <!-- Students Count -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.studentsCount">
                  <div class="text-center" [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.studentsCount || 0 }}
                  </div>
                </ng-container>

                <!-- Parent Name -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.parentFirstName">
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.parentFirstName }} {{ group.parentLastName }}
                  </div>
                </ng-container>

                <!-- Parent Email -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.parentPrimaryEmail">
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.parentPrimaryEmail }}
                  </div>
                </ng-container>

                <!-- Active Packages -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.numberOfActivePackages">
                  <div class="text-center" [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.numberOfActivePackages || 0 }}
                  </div>
                </ng-container>

                <!-- Inactive Packages -->
                <ng-container *ngSwitchCase="searchStudentGroupsDtoFieldNames.numberOfInActivePackages">
                  <div class="text-center" [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group.numberOfInActivePackages || 0 }}
                  </div>
                </ng-container>

                <!-- Default case for any other fields -->
                <ng-container *ngSwitchDefault>
                  <div [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ group[col.field] }}
                  </div>
                </ng-container>

              </ng-container>
            </td>
          </tr>
        </ng-template>

        <!-- Expand Template - Students List -->
        <ng-template pTemplate="expandedrow" let-group>
          <tr>
            <td colspan="100%">
              <div class="expansion-content">
                <div class="flex align-items-center justify-content-between mb-3">
                  <h5 class="m-0 text-900">
                    <i class="pi pi-users mr-2"></i>
                    Students in {{ group.groupName }}
                  </h5>
                  <span class="text-500 text-sm">{{ getStudentCount(group) }} student(s)</span>
                </div>
                
                <div *ngIf="hasStudents(group); else noStudents" 
                     class="student-grid">
                  <div *ngFor="let student of group.students; trackBy: trackByStudentId" 
                       class="student-card surface-card p-3 border-round shadow-1">
                    <div class="flex align-items-center gap-3">
                      <div class="flex-shrink-0">
                        <lib-prime-profile-photo-single
                          [src]="student.profilePhotoUrl"
                          [width]="48"
                          [height]="48"
                          [alt]="student.firstName + ' ' + student.lastName"
                          [placeholder]="(student.firstName?.charAt(0) || '') + (student.lastName?.charAt(0) || '')"
                          customClass="border-circle"
                          [canUpload]="false">
                        </lib-prime-profile-photo-single>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="font-semibold text-900 truncate">
                          {{ student.firstName }} {{ student.lastName }}

                          <div *ngIf="student.dateOfBirth" class="text-500 text-xs truncate">
                         
                          {{ generalService.calculateAge(student.dateOfBirth) + ' years old' }}
                        </div>
                        </div>
                        <div *ngIf="student.gender" class="text-500 text-sm truncate">
                          {{ enumDropdownOptionsService.getGenderDisplayText(student.gender) }}
                        </div>
                        <div class="text-500 text-sm truncate">
                          {{ student.loginUsername }}
                        </div>
                        <div *ngIf="student.timeZoneIana" class="text-500 text-xs truncate">
                          <i class="pi pi-clock mr-1"></i>
                          {{ student.timeZoneIana }}
                        </div>
                        
                      </div>
                    </div>
                  </div>
                </div>
                
                <ng-template #noStudents>
                  <div class="text-center p-4">
                    <i class="pi pi-users text-400 text-4xl mb-3"></i>
                    <p class="text-500 m-0">No students assigned to this group</p>
                  </div>
                </ng-template>
              </div>
            </td>
          </tr>
        </ng-template>

        <!-- Table Footer -->
        <ng-template pTemplate="footer">
          <app-data-grid-header-footer
            [columns]="selectedColumns()"
            [sortable]="true"
            [reorderable]="true"
            [showHeader]="false"
            [showFooter]="true"
            [showActionsColumn]="true"
            [showExpansionColumn]="true"
            [expansionColumnWidth]="'3rem'"
            actionsColumnHeader="Actions"
            actionsColumnWidth="100px">
          </app-data-grid-header-footer>
        </ng-template>

        <!-- Empty Message -->
          <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="selectedColumns().length + 2" class="text-center p-4">
            <div class="flex flex-column align-items-center gap-3 py-5">
              <i class="pi pi-users text-4xl text-400"></i>
              <p class="text-xl text-600 m-0">No groups found</p>
              <p class="text-600 m-0">Try adjusting your search criteria or filters</p>
            </div>
          </td>
        </tr>
      </ng-template>
      </p-table>
    </div>
  </div>
</div>
