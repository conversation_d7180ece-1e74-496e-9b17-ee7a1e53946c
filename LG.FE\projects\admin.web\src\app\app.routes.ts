import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { LayoutComponent } from './shared/components/layout/layout.component';
import { AuthRedirectGuard } from './core/guards/auth-redirect.guard';

export const routes: Routes = [
    {
        canActivate: [AuthRedirectGuard],
        path: 'auth/login',
        loadComponent: () => import('./auth/login/login.component').then(m => m.LoginComponent)
    },
    // Public routes (without AuthGuard)
    {
        path: '', redirectTo: "auth/login", pathMatch: "full"
    },
    {
        path: 'dashboard',
        data: {
            showSidebar: true,
        },
        canActivate: [AuthGuard],
        component: LayoutComponent,
        children: [
            {
                path: 'overview',
                loadComponent: () =>
                    import('./features/main-overview/main-overview.component').then(
                        (m) => m.MainOverviewComponent
                    ),
            },
            {
                path: 'students',
                loadComponent: () =>
                    import('./features/students/students.component').then(
                        (m) => m.StudentsComponent
                    ),
                children: [
                    {
                        path: 'list',
                        loadComponent: () =>
                            import('./features/students/students-list/students-list.component').then(
                                (m) => m.StudentsListComponent
                            ),
                    },
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    }
                ],
            },
            {
                path: 'teachers',
                loadComponent: () =>
                    import('./features/teachers/teachers.component').then(
                        (m) => m.TeachersComponent
                    ),
                children: [
                    {
                        path: 'list',
                        loadComponent: () =>
                            import('./features/teachers/teachers-list/teachers-list.component').then(
                                (m) => m.TeachersListComponent
                            ),
                    },
                    {
                        path: 'add',
                        loadComponent: () =>
                            import('./features/teachers/teachers-add/teachers-add.component').then(
                                (m) => m.TeachersAddComponent
                            ),
                    },
                    {
                        path: 'days-off',
                        loadComponent: () =>
                            import('./features/teachers/days-off-list/days-off-list.component').then(
                                (m) => m.DaysOffListComponent
                            ),
                    },
                    {
                        path: ':id',
                        loadComponent: () =>
                            import('./features/profiles/teacher/teacher-profile/teacher-profile.component').then(
                                (m) => m.TeacherProfileComponent
                            ),
                        children: [
                            {
                                path: 'overview',
                                loadComponent: () =>
                                    import('./features/profiles/teacher/teacher-profile-overview/teacher-profile-overview.component').then(
                                        (m) => m.TeacherProfileOverviewComponent
                                    ),
                            },
                            {
                                path: 'days-off',
                                loadComponent: () =>
                                    import('./features/profiles/teacher/teacher-profile/teacher-profile-days-off/teacher-profile-days-off.component').then(
                                        (m) => m.TeacherProfileDaysOffComponent
                                    ),
                            },
                            {
                                path: 'availability-timeslots',
                                loadComponent: () =>
                                    import('./features/profiles/teacher/teacher-profile/teacher-profile-availability-timeslots/teacher-profile-availability-timeslots.component').then(
                                        (m) => m.TeacherProfileAvailabilityTimeslotsComponent
                                    ),
                            },
                            {
                                path: 'work-profile',
                                loadComponent: () =>
                                    import('SharedModules.Library').then(
                                        (m) => m.SettingsWorkProfileComponent
                                    ),
                            },
                            {
                                path: 'user-profile',
                                loadComponent: () =>
                                    import('SharedModules.Library').then(
                                        (m) => m.SettingsAccountInfoCardComponent
                                    ),
                            },
                        ],
                    },
                    {
                        path: ':id/overview',
                        loadComponent: () =>
                            import('./features/profiles/teacher/teacher-profile/teacher-profile.component').then(
                                (m) => m.TeacherProfileComponent
                            ),
                    },
                    {
                        path: ':id/days-off',
                        loadComponent: () =>
                            import('./features/profiles/teacher/teacher-profile/teacher-profile-days-off/teacher-profile-days-off.component').then(
                                (m) => m.TeacherProfileDaysOffComponent
                            ),
                    },
                ],
            },
            {
                path: 'groups',
                loadComponent: () =>
                    import('./features/groups/groups.component').then(
                        (m) => m.GroupsComponent
                    ),
                children: [
                    {
                        path: 'list',
                        loadComponent: () =>
                            import('./features/groups/groups-list/groups-list.component').then(
                                (m) => m.GroupsListComponent
                            ),
                    },
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    }
                ],
            },
            {
                path: 'teacher-change-requests',
                children: [
                    {
                        path: 'list',
                        loadComponent: () =>
                            import('./features/teacher-change-requests/teacher-change-requests-list/teacher-change-requests-list.component').then(
                                (m) => m.TeacherChangeRequestsListComponent
                            ),
                    },
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    }
                ],
            },
            {
                path: 'teaching-languages',
                loadComponent: () =>
                    import('./features/teaching-languages-list/teaching-languages-list.component').then(
                        (m) => m.TeachingLanguagesListComponent
                    ),
            },
            {
                path: 'student-registrations-list',
                loadComponent: () =>
                    import('./features/general/direct-student-registrations-list/direct-student-registrations-list.component').then(
                        (m) => m.DirectStudentRegistrationsListComponent
                    ),
            },
            {
                path: 'prices',
                loadComponent: () =>
                    import('./features/prices-list/prices-list.component').then(
                        (m) => m.PricesListComponent
                    ),
            },
            {
                path: 'settings',
                loadComponent: () =>
                    import('./features/user-settings/user-settings.component').then(
                        (m) => m.UserSettingsComponent
                    ),
            },
        ]
    },
    // Always put the wild-card route last
    {
        path: '**', pathMatch: 'full',
        loadComponent: () =>
            import('./shared/components/not-found/not-found.component').then(
                (m) => m.NotFoundComponent
            ),
    },
];
